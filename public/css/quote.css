html, body {
    height: 100%;
    margin: 0;
    background-image: url('../images/quote-bg.png'); 
    background-repeat: no-repeat;
    background-position: bottom center;
}

.fix-quote-centered-form {
    display: flex;
    flex-direction: column; 
    justify-content: center;
    align-items: center;
    padding: 0 20px;
}

.fix-quote-logo {
    margin-bottom: 80px; 
    max-width: 300px;
    width: 100%;
    height: 100%;
}



form {
    max-width: 350px;
}

@media (max-width: 768px) {
.fix-quote-logo {
max-width: 300px;
margin-bottom:30px; 
}

body {
    padding-top: 30px;
}
}

.dropdown-icon {
  right: 15px !important;
  width: 20px !important;
  height: 12px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin-top: 0 !important;
}

/* Ensure select-custom containers work properly on quote page */
.select-custom {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
}

.select-custom .styled-select {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  padding-right: 45px !important;
}

@media (max-width: 768px) {
  .dropdown-icon {
    right: 12px !important;
    width: 18px !important;
    height: 10px !important;
  }
}

@media (max-width: 480px) {
  .dropdown-icon {
    right: 10px !important;
    width: 16px !important;
    height: 9px !important;
  }
}